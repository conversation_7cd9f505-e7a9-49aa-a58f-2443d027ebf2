/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */

package com.jdx.rover.mobile.web.jsf.control;

import com.jdx.rover.common.utils.ParameterCheckUtility;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.mobile.web.jsf.control.service.MobileRemoteCommandJsfService;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.service.mobile.CommandService;
import com.jdx.rover.monitor.vo.MiniMonitorRemoteShoutVO;
import com.jdx.rover.monitor.vo.mobile.command.SendVO;
import com.jdx.rover.monitor.vo.mobile.common.VehicleBaseVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/17 21:49
 * @description MobileRemoteCommandJsfServiceImpl
 */
@Service
@RequiredArgsConstructor
public class MobileRemoteCommandJsfServiceProvider extends AbstractProvider<MobileRemoteCommandJsfService> implements MobileRemoteCommandJsfService {

    /**
     * CommandService
     */
    private final CommandService commandService;

    @Override
    @ServiceInfo(name = "指令操作", webUrl = "/mobile/applet/command/send")
    public HttpResult<Void> send(SendVO sendVO) {
        MonitorErrorEnum monitorErrorEnum = commandService.send(sendVO);
        return HttpResult.error(monitorErrorEnum.getCode(), monitorErrorEnum.getMessage());
    }

    @Override
    @ServiceInfo(name = "声音播报", webUrl = "/mobile/applet/command/broadCast")
    public HttpResult<Void> remoteShoutWord(MiniMonitorRemoteShoutVO miniMonitorRemoteShoutVO) {
        ParameterCheckUtility.checkNotNull(miniMonitorRemoteShoutVO.getVoiceMsg(), "miniMonitorRemoteShoutVO#msg");
        commandService.remoteBroadCastWord(miniMonitorRemoteShoutVO.getVehicleName(), miniMonitorRemoteShoutVO);
        return HttpResult.success();
    }

    @Override
    @ServiceInfo(name = "声音鸣笛", webUrl = "/mobile/applet/command/whistle")
    public HttpResult<Void> remoteVoiceWhistle(VehicleBaseVO vehicleBaseVO) {
        commandService.remoteVoiceWhistle(vehicleBaseVO.getVehicleName());
        return HttpResult.success();
    }
}
