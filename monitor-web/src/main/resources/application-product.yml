server:
  port: 8081
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: redis-xvmv718wpfnw-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
impassable-area:
  addUrl: http://xmapvis.jd.local/AddTnta
  delUrl: http://xmapvis.jd.local/DelTnta
  searchUrl: http://xmapvis.jd.local/ReqTntaByPos
  updateUrl: http://xmapvis.jd.local/UpdateTntaByEffectDate
  utmUrl: http://xcss-cloud.jd.local/xcss/v0
rover-video:
  url: http://rover-video-lbs.jd.local/url/play/
  multiUrl: http://rover-video-lbs.jd.local/video/lbs/url/play/quantity
  snapshotUrl: http://rover-video-process.jd.local/video/process/snapshot/realtime
  historyPicUrl: http://rover-video-process.jd.local/video/process/snapshot/history
  historySearchUrl: http://rover-video-process.jd.local/video/process/history/contain
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
rover-map:
  rover-map-bucket-name: rover-map
  attachment-bucket-name: rover-jira
jmq:
  address: nameserver.jmq.jd.local:80
  password: b0aaaab078e54f219584c9d1dcd6eb78
  app: roverautomonitorweb
monitor:
  jmq:
    provider:
      topic:
        monitor_cockpit_realtime_status_change: cockpit_realtime_status_change
        monitor_remote_command_log: monitor_remote_command_log
        shadow_tracking_event: rover_shadow_tracking_event
        shadow_jira_event: rover_shadow_jira_event
        monitor_pnc_route: monitor_pnc_route
        server_web_terminal_command_down: server_web_terminal_command_down
        monitor_vehicle_remote_command_operation: vehicle_remote_command_operation
        monitor_remote_control_command: monitor_remote_control_command
        monitor_manual_vehicle_alarm: monitor_manual_vehicle_alarm
        monitor_view_order_detail: monitor_view_order_detail
        monitor_vehicle_change_event: monitor_vehicle_change_event
        monitor_single_vehicle_realtime: monitor_single_vehicle_realtime
        monitor_single_vehicle_schedule: monitor_single_vehicle_schedule
        monitor_accident_flow_event: monitor_accident_flow_event
        map_vehicle_alarm: map_vehicle_alarm
        monitor_map_collection_takeover: monitor_map_collection_takeover
ducc:
  config:
    name: monitor_web_config
    uri: ucc://jdos_rover-auto-monitor-web:<EMAIL>/v1/namespace/rover_monitor_web/config/monitor_web_config/profiles/product?longPolling=60000&necessary=true