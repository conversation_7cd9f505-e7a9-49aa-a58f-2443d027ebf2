/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.config.ducc;

import com.jd.laf.config.spring.annotation.LafValue;
import java.util.List;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * <p>
 * DUCC配置
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/26
 * @version 1.0.0
 */
@Component
@Data
@Validated
public class DuccConfigProperties {

    /**
     * <p>
     * 机动车道行驶监控区域
     * </p>
     */
    @LafValue("monitor.drive.lane.region")
    private String monitorDrivingLaneRegion;

    /**
     * <p>
     * 机动车道行驶过滤监控区域
     * </p>
     */
    @LafValue("monitor.drive.lane.filter.region")
    private String monitorDrivingLaneFilterRegion;

    /**
    * 多车页6FE车辆配置
    */
    @LafValue("monitor.multi.vehicle.page")
    private List<String> monitorMultiVehiclePage;

    /**
     * DUCC动态配置：车辆是否处于运动状态最小速度，单位m/s
     */
    @LafValue(key = "monitor.work.map.minDrivingSpeed")
    private Double minDrivingSpeed;

    /**
     * DUCC动态配置：车辆每秒最大行驶距离，单位m
     */
    @LafValue(key = "monitor.work.map.maxDrivingDistance")
    private Integer maxDrivingDistance;

    /**
     * 地图采集任务定时上传任务间隔，单位：分钟
     */
    @LafValue(key = "monitor.web.map.mapTaskAutoUploadTimeInterval")
    private Integer mapTaskAutoUploadTimeInterval;

    /**
     * DUCC动态配置：是否开启xata数据拷贝
     */
    @LafValue(key = "monitor.web.map.openXataDataCopy")
    private Boolean openXataDataCopy;
}